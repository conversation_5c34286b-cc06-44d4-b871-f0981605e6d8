2025-07-15 23:07:33.011 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-15 23:07:33.014 | Loaded RSA public key for plugin verification
2025-07-15 23:07:33.063 | GitHub repositories enabled in configuration
2025-07-15 23:07:33.067 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-15 23:07:33.067 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-15 23:07:33.067 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-15 23:07:33.068 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-15 23:07:33.071 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-15 23:07:33.071 | Using Consul URL: consul:8500
2025-07-15 23:07:33.103 | Successfully initialized repository of type: local
2025-07-15 23:07:33.103 | Successfully initialized repository of type: mongo
2025-07-15 23:07:33.106 | Successfully initialized repository of type: librarian-definition
2025-07-15 23:07:33.107 | Successfully initialized repository of type: git
2025-07-15 23:07:33.107 | Initializing GitHub repository with provided credentials
2025-07-15 23:07:33.107 | GitHubRepository: Initialized for cpravetz/s7plugins. Plugins dir: 'plugins'. Default branch from config/env: main
2025-07-15 23:07:33.107 | Successfully initialized repository of type: github
2025-07-15 23:07:33.108 | Refreshing plugin cache...
2025-07-15 23:07:33.108 | Loading plugins from local repository...
2025-07-15 23:07:33.109 | LocalRepo: Loading fresh plugin list
2025-07-15 23:07:33.109 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-15 23:07:33.110 | Refreshing plugin cache...
2025-07-15 23:07:33.110 | Loading plugins from local repository...
2025-07-15 23:07:33.110 | LocalRepo: Loading fresh plugin list
2025-07-15 23:07:33.110 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-15 23:07:33.116 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-15 23:07:33.135 | LocalRepo: Loading from  [
2025-07-15 23:07:33.135 |   'ACCOMPLISH',
2025-07-15 23:07:33.135 |   'API_CLIENT',
2025-07-15 23:07:33.135 |   'CHAT',
2025-07-15 23:07:33.135 |   'CODE_EXECUTOR',
2025-07-15 23:07:33.135 |   'DATA_TOOLKIT',
2025-07-15 23:07:33.135 |   'FILE_OPS_PYTHON',
2025-07-15 23:07:33.135 |   'GET_USER_INPUT',
2025-07-15 23:07:33.135 |   'SCRAPE',
2025-07-15 23:07:33.135 |   'SEARCH_PYTHON',
2025-07-15 23:07:33.135 |   'TASK_MANAGER',
2025-07-15 23:07:33.135 |   'TEXT_ANALYSIS',
2025-07-15 23:07:33.135 |   'WEATHER'
2025-07-15 23:07:33.135 | ]
2025-07-15 23:07:33.135 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-15 23:07:33.136 | LocalRepo: Loading from  [
2025-07-15 23:07:33.137 |   'ACCOMPLISH',
2025-07-15 23:07:33.137 |   'API_CLIENT',
2025-07-15 23:07:33.137 |   'CHAT',
2025-07-15 23:07:33.137 |   'CODE_EXECUTOR',
2025-07-15 23:07:33.137 |   'DATA_TOOLKIT',
2025-07-15 23:07:33.137 |   'FILE_OPS_PYTHON',
2025-07-15 23:07:33.137 |   'GET_USER_INPUT',
2025-07-15 23:07:33.137 |   'SCRAPE',
2025-07-15 23:07:33.137 |   'SEARCH_PYTHON',
2025-07-15 23:07:33.137 |   'TASK_MANAGER',
2025-07-15 23:07:33.137 |   'TEXT_ANALYSIS',
2025-07-15 23:07:33.137 |   'WEATHER'
2025-07-15 23:07:33.137 | ]
2025-07-15 23:07:33.137 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-15 23:07:33.181 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-15 23:07:33.189 | Service CapabilitiesManager registered with Consul
2025-07-15 23:07:33.189 | Successfully registered CapabilitiesManager with Consul
2025-07-15 23:07:33.189 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-15 23:07:33.190 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-15 23:07:33.194 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-15 23:07:33.195 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-15 23:07:33.195 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-15 23:07:33.196 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-15 23:07:33.197 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-15 23:07:33.197 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-15 23:07:33.198 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-15 23:07:33.198 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-15 23:07:33.199 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-15 23:07:33.200 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-15 23:07:33.201 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-15 23:07:33.201 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-15 23:07:33.202 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-15 23:07:33.202 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-15 23:07:33.203 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-15 23:07:33.204 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-15 23:07:33.204 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-15 23:07:33.205 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-15 23:07:33.206 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-15 23:07:33.207 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-15 23:07:33.208 | LocalRepo: Locators count 12
2025-07-15 23:07:33.211 | CapabilitiesManager registered successfully with PostOffice
2025-07-15 23:07:33.211 | LocalRepo: Locators count 12
2025-07-15 23:07:33.212 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-15 23:07:33.212 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-15 23:07:33.213 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-15 23:07:33.213 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-15 23:07:33.214 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-15 23:07:33.214 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-15 23:07:33.215 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-15 23:07:33.215 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-15 23:07:33.215 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-15 23:07:33.216 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-15 23:07:33.216 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-15 23:07:33.217 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-15 23:07:33.217 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-15 23:07:33.218 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-15 23:07:33.218 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-15 23:07:33.218 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-15 23:07:33.219 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-15 23:07:33.219 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-15 23:07:33.220 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-15 23:07:33.220 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-15 23:07:33.221 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-15 23:07:33.222 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-15 23:07:33.222 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-15 23:07:33.222 | Loaded 12 plugins from local repository
2025-07-15 23:07:33.222 | Loading plugins from mongo repository...
2025-07-15 23:07:33.227 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-15 23:07:33.227 | Loaded 12 plugins from local repository
2025-07-15 23:07:33.227 | Loading plugins from mongo repository...
2025-07-15 23:07:34.636 | Loaded 0 plugins from mongo repository
2025-07-15 23:07:34.636 | Loading plugins from librarian-definition repository...
2025-07-15 23:07:34.646 | Loaded 0 plugins from mongo repository
2025-07-15 23:07:34.646 | Loading plugins from librarian-definition repository...
2025-07-15 23:07:34.661 | Loaded 0 plugins from librarian-definition repository
2025-07-15 23:07:34.663 | Loading plugins from git repository...
2025-07-15 23:07:34.667 | Loaded 0 plugins from librarian-definition repository
2025-07-15 23:07:34.667 | Loading plugins from git repository...
2025-07-15 23:07:34.697 | Failed to list plugins from Git repository: Cloning into '/usr/src/app/services/capabilitiesmanager/temp/list-plugins'...
2025-07-15 23:07:34.697 | fatal: cannot copy '/usr/share/git-core/templates/info/exclude' to '/usr/src/app/services/capabilitiesmanager/temp/list-plugins/.git/info/exclude': File exists
2025-07-15 23:07:34.697 | 
2025-07-15 23:07:34.698 | Failed to list plugins from Git repository: Cloning into '/usr/src/app/services/capabilitiesmanager/temp/list-plugins'...
2025-07-15 23:07:34.698 | /usr/src/app/services/capabilitiesmanager/temp/list-plugins/.git/hooks/: No such file or directory
2025-07-15 23:07:34.698 | 
2025-07-15 23:07:34.699 | Loaded 0 plugins from git repository
2025-07-15 23:07:34.699 | Loading plugins from github repository...
2025-07-15 23:07:34.796 | Loaded 0 plugins from git repository
2025-07-15 23:07:34.796 | Loading plugins from github repository...
2025-07-15 23:07:35.001 | Loaded 0 plugins from github repository
2025-07-15 23:07:35.001 | Plugin cache refreshed. Total plugins: 12
2025-07-15 23:07:35.001 | PluginRegistry initialized and cache populated.
2025-07-15 23:07:35.001 | PluginRegistry: Registered verbs after cache refresh: [
2025-07-15 23:07:35.001 |   'ACCOMPLISH',
2025-07-15 23:07:35.001 |   'API_CLIENT',
2025-07-15 23:07:35.001 |   'CHAT',
2025-07-15 23:07:35.001 |   'RUN_CODE',
2025-07-15 23:07:35.001 |   'DATA_TOOLKIT',
2025-07-15 23:07:35.001 |   'FILE_OPERATION',
2025-07-15 23:07:35.001 |   'ASK_USER_QUESTION',
2025-07-15 23:07:35.001 |   'SCRAPE',
2025-07-15 23:07:35.001 |   'SEARCH',
2025-07-15 23:07:35.001 |   'TASK_MANAGER',
2025-07-15 23:07:35.001 |   'TEXT_ANALYSIS',
2025-07-15 23:07:35.001 |   'WEATHER'
2025-07-15 23:07:35.001 | ]
2025-07-15 23:07:35.001 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-07-15 23:07:35.001 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-15 23:07:35.001 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-15 23:07:35.001 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-15 23:07:35.001 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-15 23:07:35.001 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:267:37)
2025-07-15 23:07:35.001 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:113:13)
2025-07-15 23:07:35.001 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-15 23:07:35.001 |   'plugin-ACCOMPLISH',
2025-07-15 23:07:35.001 |   'plugin-API_CLIENT',
2025-07-15 23:07:35.001 |   'plugin-CHAT',
2025-07-15 23:07:35.001 |   'plugin-CODE_EXECUTOR',
2025-07-15 23:07:35.001 |   'plugin-DATA_TOOLKIT',
2025-07-15 23:07:35.001 |   'plugin-FILE_OPS_PYTHON',
2025-07-15 23:07:35.001 |   'plugin-ASK_USER_QUESTION',
2025-07-15 23:07:35.001 |   'plugin-SCRAPE',
2025-07-15 23:07:35.001 |   'plugin-SEARCH_PYTHON',
2025-07-15 23:07:35.001 |   'plugin-TASK_MANAGER',
2025-07-15 23:07:35.001 |   'plugin-TEXT_ANALYSIS',
2025-07-15 23:07:35.001 |   'plugin-WEATHER'
2025-07-15 23:07:35.001 | ]
2025-07-15 23:07:35.011 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-15 23:07:35.011 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-15 23:07:35.011 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-15 23:07:35.011 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-15 23:07:35.011 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:267:37)
2025-07-15 23:07:35.011 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:113:13)
2025-07-15 23:07:35.011 |     at async CapabilitiesManager.initialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:78:21)
2025-07-15 23:07:35.011 |     at async tryInitialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:54:17)
2025-07-15 23:07:35.011 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-15 23:07:35.011 | Loaded 0 plugins from github repository
2025-07-15 23:07:35.011 | Plugin cache refreshed. Total plugins: 12
2025-07-15 23:07:35.011 | PluginRegistry initialized and cache populated.
2025-07-15 23:07:35.011 | PluginRegistry: Registered verbs after cache refresh: [
2025-07-15 23:07:35.011 |   'ACCOMPLISH',
2025-07-15 23:07:35.011 |   'API_CLIENT',
2025-07-15 23:07:35.011 |   'CHAT',
2025-07-15 23:07:35.011 |   'RUN_CODE',
2025-07-15 23:07:35.011 |   'DATA_TOOLKIT',
2025-07-15 23:07:35.011 |   'FILE_OPERATION',
2025-07-15 23:07:35.011 |   'ASK_USER_QUESTION',
2025-07-15 23:07:35.011 |   'SCRAPE',
2025-07-15 23:07:35.011 |   'SEARCH',
2025-07-15 23:07:35.011 |   'TASK_MANAGER',
2025-07-15 23:07:35.011 |   'TEXT_ANALYSIS',
2025-07-15 23:07:35.011 |   'WEATHER'
2025-07-15 23:07:35.011 | ]
2025-07-15 23:07:35.012 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-07-15 23:07:35.012 |   'plugin-ACCOMPLISH',
2025-07-15 23:07:35.012 |   'plugin-API_CLIENT',
2025-07-15 23:07:35.012 |   'plugin-CHAT',
2025-07-15 23:07:35.012 |   'plugin-CODE_EXECUTOR',
2025-07-15 23:07:35.012 |   'plugin-DATA_TOOLKIT',
2025-07-15 23:07:35.012 |   'plugin-FILE_OPS_PYTHON',
2025-07-15 23:07:35.012 |   'plugin-ASK_USER_QUESTION',
2025-07-15 23:07:35.012 |   'plugin-SCRAPE',
2025-07-15 23:07:35.012 |   'plugin-SEARCH_PYTHON',
2025-07-15 23:07:35.012 |   'plugin-TASK_MANAGER',
2025-07-15 23:07:35.012 |   'plugin-TEXT_ANALYSIS',
2025-07-15 23:07:35.012 |   'plugin-WEATHER'
2025-07-15 23:07:35.012 | ]
2025-07-15 23:07:35.012 | [CapabilitiesManager-constructor-318167de] CapabilitiesManager.initialize: PluginRegistry initialized.
2025-07-15 23:07:35.012 | [CapabilitiesManager-constructor-318167de] CapabilitiesManager.initialize: ConfigManager initialized.
2025-07-15 23:07:35.013 | [CapabilitiesManager-constructor-318167de] Setting up express server...
2025-07-15 23:07:35.020 | [CapabilitiesManager-constructor-318167de] CapabilitiesManager server listening on port 5060
2025-07-15 23:07:35.020 | [CapabilitiesManager-constructor-318167de] CapabilitiesManager server setup complete
2025-07-15 23:07:35.020 | [CapabilitiesManager-constructor-318167de] CapabilitiesManager.initialize: CapabilitiesManager initialization completed.
2025-07-15 23:07:43.215 | Connected to RabbitMQ
2025-07-15 23:07:43.220 | Channel created successfully
2025-07-15 23:07:43.220 | RabbitMQ channel ready
2025-07-15 23:07:43.278 | Connection test successful - RabbitMQ connection is stable
2025-07-15 23:07:43.278 | Creating queue: capabilitiesmanager-CapabilitiesManager
2025-07-15 23:07:43.289 | Binding queue to exchange: stage7
2025-07-15 23:07:43.301 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-15 23:32:59.750 | Created ServiceTokenManager for CapabilitiesManager
2025-07-15 23:32:59.764 | In executeAccomplishPlugin
2025-07-15 23:32:59.764 | LocalRepo: Loading fresh plugin list
2025-07-15 23:32:59.764 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-15 23:32:59.767 | LocalRepo: Loading from  [
2025-07-15 23:32:59.767 |   'ACCOMPLISH',
2025-07-15 23:32:59.767 |   'API_CLIENT',
2025-07-15 23:32:59.767 |   'CHAT',
2025-07-15 23:32:59.767 |   'CODE_EXECUTOR',
2025-07-15 23:32:59.767 |   'DATA_TOOLKIT',
2025-07-15 23:32:59.767 |   'FILE_OPS_PYTHON',
2025-07-15 23:32:59.767 |   'GET_USER_INPUT',
2025-07-15 23:32:59.767 |   'SCRAPE',
2025-07-15 23:32:59.767 |   'SEARCH_PYTHON',
2025-07-15 23:32:59.767 |   'TASK_MANAGER',
2025-07-15 23:32:59.767 |   'TEXT_ANALYSIS',
2025-07-15 23:32:59.767 |   'WEATHER'
2025-07-15 23:32:59.767 | ]
2025-07-15 23:32:59.767 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-15 23:32:59.769 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-15 23:32:59.771 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-15 23:32:59.773 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-15 23:32:59.774 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-15 23:32:59.775 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-15 23:32:59.777 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-15 23:32:59.778 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-15 23:32:59.778 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-15 23:32:59.780 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-15 23:32:59.781 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-15 23:32:59.782 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-15 23:32:59.783 | LocalRepo: Locators count 12
2025-07-15 23:32:59.783 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-15 23:32:59.784 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-15 23:32:59.785 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-15 23:32:59.787 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-15 23:32:59.787 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-15 23:32:59.787 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-15 23:32:59.788 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-15 23:32:59.789 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-15 23:32:59.789 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-15 23:32:59.790 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-15 23:32:59.791 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-15 23:32:59.791 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-15 23:33:00.441 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.executeAccomplishPlugin: Plugins string for ACCOMPLISH: - DELEGATE: Create sub-agents with goals of their own.
2025-07-15 23:33:00.441 | - THINK: - sends prompts to the chat function...
2025-07-15 23:33:00.441 | PluginRegistry.fetchOneByVerb called for verb: ACCOMPLISH
2025-07-15 23:33:00.441 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-15 23:33:00.441 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-15 23:33:00.441 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-15 23:33:00.441 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-15 23:33:00.441 |     at async PluginMarketplace.getAvailablePluginsStr (/usr/src/app/marketplace/dist/PluginMarketplace.js:356:34)
2025-07-15 23:33:00.441 |     at async PluginRegistry.getAvailablePluginsStr (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:302:20)
2025-07-15 23:33:00.441 |     at async CapabilitiesManager.executeAccomplishPlugin (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:1178:35)
2025-07-15 23:33:00.441 |     at async CapabilitiesManager.executeActionVerb (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:361:47)
2025-07-15 23:33:00.441 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-15 23:33:00.443 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-15 23:33:00.444 | Using inline plugin path for plugin-ACCOMPLISH (ACCOMPLISH): /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-15 23:33:00.445 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.executePlugin: Executing plugin plugin-ACCOMPLISH v1.0.0 (ACCOMPLISH) at /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-15 23:33:00.474 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.executePythonPlugin: Python execution - Main file path: /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/main.py, Root path: /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-15 23:33:00.497 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.ensurePythonDependencies: Found python executable with command: python3 --version
2025-07-15 23:33:00.497 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.ensurePythonDependencies: Creating virtual environment at /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv.
2025-07-15 23:33:00.497 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.ensurePythonDependencies: Running command: python3 -m venv "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv"
2025-07-15 23:33:06.193 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.ensurePythonDependencies: Upgrading pip with command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/pip" install --upgrade pip
2025-07-15 23:33:10.222 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.ensurePythonDependencies: Installing requirements with command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/pip" install -r "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt"
2025-07-15 23:33:12.741 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.ensurePythonDependencies: Python dependency installation stdout: Collecting requests>=2.28.0 (from -r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-15 23:33:12.741 |   Downloading requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
2025-07-15 23:33:12.741 | Collecting charset_normalizer<4,>=2 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-15 23:33:12.741 |   Downloading charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl.metadata (35 kB)
2025-07-15 23:33:12.741 | Collecting idna<4,>=2.5 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-15 23:33:12.741 |   Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)
2025-07-15 23:33:12.741 | Collecting urllib3<3,>=1.21.1 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-15 23:33:12.741 |   Downloading urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
2025-07-15 23:33:12.741 | Collecting certifi>=2017.4.17 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-15 23:33:12.741 |   Downloading certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)
2025-07-15 23:33:12.741 | Downloading requests-2.32.4-py3-none-any.whl (64 kB)
2025-07-15 23:33:12.741 | Downloading charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl (149 kB)
2025-07-15 23:33:12.741 | Downloading idna-3.10-py3-none-any.whl (70 kB)
2025-07-15 23:33:12.741 | Downloading urllib3-2.5.0-py3-none-any.whl (129 kB)
2025-07-15 23:33:12.741 | Downloading certifi-2025.7.14-py3-none-any.whl (162 kB)
2025-07-15 23:33:12.741 | Installing collected packages: urllib3, idna, charset_normalizer, certifi, requests
2025-07-15 23:33:12.741 | 
2025-07-15 23:33:12.741 | Successfully installed certifi-2025.7.14 charset_normalizer-3.4.2 idna-3.10 requests-2.32.4 urllib3-2.5.0
2025-07-15 23:33:12.741 | 
2025-07-15 23:33:12.744 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.ensurePythonDependencies: Python dependencies processed successfully for /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH. Marker file updated.
2025-07-15 23:33:12.746 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.executePythonPlugin: Executing Python command: echo "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" | base64 -d | "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/python" "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/main.py" "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH"
2025-07-15 23:33:12.748 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.executePythonPlugin: Piping inputsJsonString to Python plugin: [["goal",{"inputName":"goal","value":"Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.","valueType":"string","args":{}}],["verbToAvoid",{"inputName":"verbToAvoid","value":"EXECUTE","valueType":"string","args":{}}],["available_plugins",{"inputName":"available_plugins","value":"- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location","valueType":"string","args":{}}],["postOffice_url",{"inputName":"postOffice_url","value":"postoffice:5020","valueType":"string","args":{}}],["brain_url",{"inputName":"brain_url","value":"brain:5070","valueType":"string","args":{}}],["librarian_url",{"inputName":"librarian_url","value":"librarian:5040","valueType":"string","args":{}}],["__auth_token",{"inputName":"__auth_token","value":"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}],["__brain_auth_token",{"inputName":"__brain_auth_token","value":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}],["token",{"inputName":"token","value":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}]]
2025-07-15 23:33:15.927 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.executePythonPlugin: Raw stderr from Python plugin ACCOMPLISH v1.0.0:
2025-07-15 23:33:15.927 | 2025-07-16 03:33:13,127 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}
2025-07-15 23:33:15.927 | 2025-07-16 03:33:13,128 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location'
2025-07-15 23:33:15.927 | 2025-07-16 03:33:13,128 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'
2025-07-15 23:33:15.927 | 2025-07-16 03:33:14,518 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat
2025-07-15 23:33:15.927 | 2025-07-16 03:33:14,519 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location'
2025-07-15 23:33:15.927 | 2025-07-16 03:33:14,519 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'
2025-07-15 23:33:15.927 | 2025-07-16 03:33:15,871 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat
2025-07-15 23:33:15.927 | 
2025-07-15 23:33:15.927 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.executePythonPlugin: Raw stdout from Python plugin ACCOMPLISH v1.0.0:
2025-07-15 23:33:15.927 | [{"success": false, "name": "error", "resultType": "ERROR", "resultDescription": "Failed to get response from Brain service.", "result": {"logs": "2025-07-16 03:33:13,127 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-16 03:33:13,128 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 03:33:13,128 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 03:33:14,518 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n2025-07-16 03:33:14,519 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 03:33:14,519 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 03:33:15,871 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n"}, "error": "Brain service unavailable."}]
2025-07-15 23:33:15.927 | 
2025-07-15 23:33:15.928 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.validatePythonOutput: Validating Python output for ACCOMPLISH v1.0.0. Received stdout:
2025-07-15 23:33:15.928 | [{"success": false, "name": "error", "resultType": "ERROR", "resultDescription": "Failed to get response from Brain service.", "result": {"logs": "2025-07-16 03:33:13,127 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-16 03:33:13,128 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 03:33:13,128 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 03:33:14,518 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n2025-07-16 03:33:14,519 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 03:33:14,519 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 03:33:15,871 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n"}, "error": "Brain service unavailable."}]
2025-07-15 23:33:15.928 | 
2025-07-15 23:33:15.928 | [102ea269-c743-4895-b737-73651324ea98] CapabilitiesManager.validatePythonOutput: Python plugin output parsed and validated successfully for ACCOMPLISH v1.0.0